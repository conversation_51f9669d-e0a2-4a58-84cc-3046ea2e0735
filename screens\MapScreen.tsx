import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, TouchableOpacity, Text, Alert, Animated } from 'react-native';
import MapView, { Marker, PROVIDER_GOOGLE, Polyline } from 'react-native-maps';
import * as Location from 'expo-location';
import { MaterialIcons } from '@expo/vector-icons';
import { supabase } from '../lib/supabase';
import { POI, POICategory } from '../types/poi';
import {
  LocationFilter,
  INITIAL_REGION,
  LOCATION_TRACKING_OPTIONS,
  MAP_OPTIONS,
  ROUTE_STYLE,
} from '../utils/LocationFilter';
import { geofenceService, GeofenceEvent, LocationContext } from '../utils/GeofenceService';

const CATEGORY_ICONS: Record<POICategory, string> = {
  beach: '🏖️',
  mountain: '⛰️',
  restaurant: '🍽️',
  port: '🚢',
  landmark: '👑',
  boarding: '🚢',
  parking: '🅿️',
  rental: '🛥️',
  excursion: '🥾',
  diving: '🤿',
  ticket_office: '🎫',
};

export default function MapScreen() {
  const mapRef = useRef<MapView>(null);
  const [pois, setPois] = useState<POI[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<Set<POICategory>>(
    new Set(['beach', 'mountain', 'restaurant', 'port', 'landmark'])
  );
  const [showUserLocation, setShowUserLocation] = useState(false);
  const [userLocation, setUserLocation] = useState<Location.LocationObjectCoords | null>(null);
  const [selectedPOI, setSelectedPOI] = useState<POI | null>(null);
  const [routeCoordinates, setRouteCoordinates] = useState<Array<{latitude: number, longitude: number}>>([]);
  const [locationFilter] = useState(() => new LocationFilter());

  // New location-aware state
  const [locationContext, setLocationContext] = useState<LocationContext>('outside_area');
  const [mapView, setMapView] = useState<'island' | 'coast'>('island');
  const [showArrivalNotification, setShowArrivalNotification] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    fetchPOIs();
    setupLocation();
    setupGeofencing();

    return () => {
      geofenceService.removeListener(handleGeofenceEvent);
    };
  }, []);

  const setupGeofencing = () => {
    geofenceService.addListener(handleGeofenceEvent);
  };

  const handleGeofenceEvent = (event: GeofenceEvent) => {
    setLocationContext(event.context);

    if (event.type === 'enter' && event.context === 'on_island') {
      showArrivalNotificationWithAnimation();
      awardIslandAccessBadge();
    }

    // Auto-adjust map view based on context
    if (event.context === 'on_island') {
      setMapView('island');
    } else if (event.context === 'coast_area' || event.context === 'outside_area') {
      setMapView('coast');
    }

    // Update POI categories based on context
    updateCategoriesForContext(event.context);
  };

  const showArrivalNotificationWithAnimation = () => {
    setShowArrivalNotification(true);

    Animated.sequence([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.delay(3000),
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setShowArrivalNotification(false);
    });
  };

  const awardIslandAccessBadge = async () => {
    try {
      // Record the location entry interaction
      const { error } = await supabase
        .from('user_interactions')
        .insert([
          {
            type: 'location_enter',
            details: { context: 'on_island' },
            location: userLocation ? {
              latitude: userLocation.latitude,
              longitude: userLocation.longitude
            } : null
          }
        ]);

      if (error) {
        console.error('Error recording island entry:', error);
      }
    } catch (error) {
      console.error('Error awarding Island Access badge:', error);
    }
  };

  const updateCategoriesForContext = (context: LocationContext) => {
    if (context === 'on_island') {
      // Show island-specific categories
      setSelectedCategories(new Set(['beach', 'mountain', 'restaurant', 'port', 'landmark']));
    } else {
      // Show off-island categories
      setSelectedCategories(new Set(['boarding', 'parking', 'rental', 'excursion', 'diving', 'ticket_office']));
    }
  };

  const toggleMapView = () => {
    const newView = mapView === 'island' ? 'coast' : 'island';
    setMapView(newView);

    const region = geofenceService.getMapRegionForContext(
      newView === 'island' ? 'on_island' : 'coast_area'
    );

    mapRef.current?.animateToRegion(region, 1000);
  };

  const setupLocation = async () => {
    const { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Errore', 'È necessario concedere i permessi GPS');
      return;
    }

    // Ritardiamo la visualizzazione della posizione di 8 secondi
    setTimeout(() => {
      setShowUserLocation(true);
    }, 8000);

    await Location.watchPositionAsync(
      {
        accuracy: Location.Accuracy.High,
        timeInterval: 1000,
        distanceInterval: 1
      },
      (location) => {
        const filteredLocation = locationFilter.addLocation(location.coords);
        setUserLocation(filteredLocation);

        // Process location for geofencing
        geofenceService.processLocationUpdate(filteredLocation);
      }
    );
  };

  const fetchPOIs = async () => {
    try {
      const { data, error } = await supabase
        .from('pois')
        .select('*')
        .order('category');

      if (error) throw error;
      setPois(data || []);
    } catch (error) {
      console.error('Error fetching POIs:', error);
    }
  };

  const getDirections = (destination: POI) => {
    if (!userLocation) return;

    setSelectedPOI(destination);
    setRouteCoordinates([
      { latitude: userLocation.latitude, longitude: userLocation.longitude },
      { latitude: destination.latitude, longitude: destination.longitude }
    ]);
  };

  const toggleCategory = (category: POICategory) => {
    setSelectedCategories(prev => {
      const newCategories = new Set(prev);
      if (newCategories.has(category)) {
        newCategories.delete(category);
      } else {
        newCategories.add(category);
      }
      return newCategories;
    });
  };

  return (
    <View style={styles.container}>
      <MapView
        ref={mapRef}
        provider={PROVIDER_GOOGLE}
        style={styles.map}
        initialRegion={INITIAL_REGION}
        showsUserLocation={showUserLocation}
        showsMyLocationButton={showUserLocation}
        showsCompass={true}
        showsUserLocationAccuracyCircle={false}
      >
        {pois
          .filter(poi => selectedCategories.has(poi.category))
          .map(poi => (
            <Marker
              key={poi.id}
              coordinate={{
                latitude: Number(poi.latitude),
                longitude: Number(poi.longitude),
              }}
              title={poi.name}
              description={poi.description}
              onPress={() => getDirections(poi)}
            >
              <Text style={styles.markerText}>
                {CATEGORY_ICONS[poi.category] || '📍'}
              </Text>
            </Marker>
          ))}

        {routeCoordinates.length > 0 && (
          <Polyline
            coordinates={routeCoordinates}
            strokeColor="#0066cc"
            strokeWidth={3}
          />
        )}
      </MapView>

      <View style={styles.categoryFilters}>
        {Object.entries(CATEGORY_ICONS).map(([category, icon]) => (
          <TouchableOpacity
            key={category}
            style={[
              styles.filterButton,
              selectedCategories.has(category as POICategory) && styles.filterButtonActive
            ]}
            onPress={() => toggleCategory(category as POICategory)}
          >
            <Text style={[
              styles.filterText,
              selectedCategories.has(category as POICategory) && styles.filterTextActive
            ]}>{icon}</Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* View Toggle Button */}
      <TouchableOpacity style={styles.viewToggleButton} onPress={toggleMapView}>
        <MaterialIcons
          name={mapView === 'island' ? 'zoom-out-map' : 'zoom-in'}
          size={24}
          color="#0066cc"
        />
        <Text style={styles.viewToggleText}>
          {mapView === 'island' ? 'Coast View' : 'Island View'}
        </Text>
      </TouchableOpacity>

      {/* Arrival Notification */}
      {showArrivalNotification && (
        <Animated.View style={[styles.arrivalNotification, { opacity: fadeAnim }]}>
          <MaterialIcons name="celebration" size={24} color="#fff" />
          <Text style={styles.arrivalText}>Benvenuto a Tavolara! 🏝️</Text>
        </Animated.View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  categoryFilters: {
    position: 'absolute',
    top: 20,
    flexDirection: 'row',
    padding: 10,
    backgroundColor: 'white',
    borderRadius: 20,
    margin: 10,
  },
  filterButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    marginHorizontal: 5,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
  },
  filterButtonActive: {
    backgroundColor: '#0066cc',
  },
  filterText: {
    color: '#666',
    fontSize: 24,
  },
  filterTextActive: {
    color: 'white',
  },
  markerText: {
    fontSize: 24,
  },
  viewToggleButton: {
    position: 'absolute',
    bottom: 100,
    right: 20,
    backgroundColor: 'white',
    borderRadius: 25,
    padding: 15,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  viewToggleText: {
    marginLeft: 8,
    color: '#0066cc',
    fontWeight: '600',
  },
  arrivalNotification: {
    position: 'absolute',
    top: 100,
    left: 20,
    right: 20,
    backgroundColor: '#4CD964',
    borderRadius: 15,
    padding: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  arrivalText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 10,
  },
});
