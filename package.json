{"name": "tavolaramap", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "^1.23.1", "@react-navigation/bottom-tabs": "^7.3.1", "@react-navigation/native": "^7.0.17", "@react-navigation/native-stack": "^7.3.1", "@supabase/supabase-js": "^2.49.1", "expo": "~52.0.39", "expo-image-picker": "^16.0.6", "expo-location": "~18.0.8", "expo-status-bar": "~2.0.1", "react": "18.3.1", "react-native": "0.76.7", "react-native-dotenv": "^3.4.11", "react-native-maps": "^1.18.0", "react-native-safe-area-context": "^4.12.0", "react-native-screens": "^4.4.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}