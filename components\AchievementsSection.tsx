import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, ScrollView } from 'react-native';
import { UserBadge } from '../types/badge';
import { ACHIEVEMENT_KEYS } from '../types/badge';
import { Ionicons } from '@expo/vector-icons';

interface AchievementsSectionProps {
  userBadges: UserBadge[];
  achievementProgress: Record<string, boolean>;
  onBadgePress: (badge: UserBadge) => void;
  onViewAllPress: () => void;
}

const AchievementsSection: React.FC<AchievementsSectionProps> = ({
  userBadges,
  achievementProgress,
  onBadgePress,
  onViewAllPress,
}) => {
  const formatAchievementName = (key: string): string => {
    switch (key) {
      case ACHIEVEMENT_KEYS.DISCOVER_KINGDOM:
        return 'Scopri il Regno';
      case ACHIEVEMENT_KEYS.ALL_BEACHES:
        return '<PERSON><PERSON> le <PERSON>gge';
      case ACHIEVEMENT_KEYS.REACH_SUMMIT:
        return 'Fino in Vetta';
      case ACHIEVEMENT_KEYS.SUNSET_BELVEDERE:
        return 'Tramonto al Belvedere';
      case ACHIEVEMENT_KEYS.NIGHT_ON_ISLAND:
        return 'Una Notte sull\'Isola';
      case ACHIEVEMENT_KEYS.UNDERWATER_VIEWS:
        return 'Viste Subacquee';
      case ACHIEVEMENT_KEYS.ISLAND_ACCESS:
        return 'Accesso all\'Isola';
      case ACHIEVEMENT_KEYS.ISLAND_FAN:
        return 'Fan dell\'Isola';
      default:
        return key.replace(/_/g, ' ');
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.headerRow}>
        <Text style={styles.sectionTitle}>I tuoi Achievement</Text>
        <TouchableOpacity onPress={onViewAllPress}>
          <Text style={styles.viewAllText}>Vedi tutti</Text>
        </TouchableOpacity>
      </View>

      {/* Badge collection */}
      {userBadges.length > 0 ? (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.badgesScrollView}
        >
          {userBadges.map((badge) => (
            <TouchableOpacity
              key={badge.id}
              style={styles.badgeItem}
              onPress={() => onBadgePress(badge)}
            >
              <Image
                source={{ uri: badge.badge?.icon_url }}
                style={styles.badgeImage}
              />
              <Text style={styles.badgeName} numberOfLines={1}>
                {badge.badge?.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      ) : (
        <View style={styles.emptyBadgesContainer}>
          <Ionicons name="ribbon-outline" size={40} color="#999" />
          <Text style={styles.emptyBadgesText}>
            Non hai ancora sbloccato badge
          </Text>
        </View>
      )}

      <View style={styles.divider} />

      {/* Achievement progress */}
      <Text style={styles.achievementsTitle}>Progresso Achievement</Text>

      {Object.entries(achievementProgress).length > 0 ? (
        <View style={styles.achievementsContainer}>
          {Object.entries(achievementProgress).map(([key, completed]) => (
            <View key={key} style={styles.achievementItem}>
              <View style={[
                styles.achievementStatus,
                { backgroundColor: completed ? '#4CD964' : '#E5E5EA' }
              ]}>
                {completed ? (
                  <Ionicons name="checkmark" size={16} color="#fff" />
                ) : (
                  <Ionicons name="time-outline" size={16} color="#8E8E93" />
                )}
              </View>
              <Text
                style={[
                  styles.achievementName,
                  completed ? styles.completedText : {}
                ]}
              >
                {formatAchievementName(key)}
              </Text>
            </View>
          ))}
        </View>
      ) : (
        <View style={styles.emptyAchievementsContainer}>
          <Text style={styles.emptyAchievementsText}>
            Inizia a esplorare per sbloccare gli achievement
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  viewAllText: {
    color: '#3498db',
    fontWeight: '500',
  },
  badgesScrollView: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  badgeItem: {
    alignItems: 'center',
    marginRight: 16,
    width: 80,
  },
  badgeImage: {
    width: 60,
    height: 60,
    marginBottom: 8,
  },
  badgeName: {
    fontSize: 12,
    textAlign: 'center',
    color: '#555',
  },
  emptyBadgesContainer: {
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    marginBottom: 15,
  },
  emptyBadgesText: {
    marginTop: 10,
    color: '#999',
    textAlign: 'center',
  },
  divider: {
    height: 1,
    backgroundColor: '#e0e0e0',
    marginBottom: 15,
  },
  achievementsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333',
  },
  achievementsContainer: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    paddingVertical: 5,
  },
  achievementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#ebebeb',
  },
  achievementStatus: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  achievementName: {
    fontSize: 15,
    color: '#555',
  },
  completedText: {
    fontWeight: '500',
    color: '#333',
  },
  emptyAchievementsContainer: {
    padding: 15,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    alignItems: 'center',
  },
  emptyAchievementsText: {
    color: '#999',
    textAlign: 'center',
  },
});

export default AchievementsSection;