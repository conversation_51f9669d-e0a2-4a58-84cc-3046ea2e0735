export type BadgeCategory =
  | 'explorer'     // Per scoperte di luoghi
  | 'activity'     // Per attività completate
  | 'collection'   // Per collezioni completate
  | 'seasonal'     // Badge stagionali
  | 'special'      // Badge speciali o eventi
  | 'location';    // Badge basati sulla posizione

export interface Badge {
  id: string;
  name: string;
  description: string;
  icon_url: string;
  category: BadgeCategory;
  requirements: string;
  created_at: string;
}

export interface UserBadge {
  id: string;
  user_id: string;
  badge_id: string;
  badge?: Badge;
  earned_at: string;
  is_featured: boolean;
}

export const ACHIEVEMENT_KEYS = {
  DISCOVER_KINGDOM: 'discover_kingdom',
  ALL_BEACHES: 'all_beaches',
  REACH_SUMMIT: 'reach_summit',
  SUNSET_BELVEDERE: 'sunset_belvedere',
  NIGHT_ON_ISLAND: 'night_on_island',
  UNDERWATER_VIEWS: 'underwater_views',
  ISLAND_ACCESS: 'island_access',
  ISLAND_FAN: 'island_fan',
} as const;

