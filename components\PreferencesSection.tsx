import React from 'react';
import { View, Text, StyleSheet, Switch, TouchableOpacity } from 'react-native';
import { UserPreferences } from '../types/auth';
import { Ionicons } from '@expo/vector-icons';

interface PreferencesSectionProps {
  preferences: UserPreferences;
  onPreferenceChange: (key: keyof UserPreferences, value: any) => void;
  onManageCategories: () => void;
  onManageActivities: () => void;
}

const PreferencesSection: React.FC<PreferencesSectionProps> = ({
  preferences,
  onPreferenceChange,
  onManageCategories,
  onManageActivities,
}) => {
  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Preferenze di Viaggio</Text>
      
      <View style={styles.preference}>
        <View style={styles.preferenceIconContainer}>
          <Ionicons name="paw" size={24} color="#3498db" />
        </View>
        <Text style={styles.preferenceText}>Viaggio con animali</Text>
        <Switch
          value={preferences.travels_with_pets}
          onValueChange={(value) => onPreferenceChange('travels_with_pets', value)}
          trackColor={{ false: '#d0d0d0', true: '#a0cfff' }}
          thumbColor={preferences.travels_with_pets ? '#3498db' : '#f4f3f4'}
        />
      </View>
      
      <View style={styles.preference}>
        <View style={styles.preferenceIconContainer}>
          <Ionicons name="people" size={24} color="#3498db" />
        </View>
        <Text style={styles.preferenceText}>Viaggio con famiglia</Text>
        <Switch
          value={preferences.travels_with_family}
          onValueChange={(value) => onPreferenceChange('travels_with_family', value)}
          trackColor={{ false: '#d0d0d0', true: '#a0cfff' }}
          thumbColor={preferences.travels_with_family ? '#3498db' : '#f4f3f4'}
        />
      </View>
      
      <View style={styles.preference}>
        <View style={styles.preferenceIconContainer}>
          <Ionicons name="accessibility" size={24} color="#3498db" />
        </View>
        <Text style={styles.preferenceText}>Esigenze di accessibilità</Text>
        <Switch
          value={preferences.has_disability_needs}
          onValueChange={(value) => onPreferenceChange('has_disability_needs', value)}
          trackColor={{ false: '#d0d0d0', true: '#a0cfff' }}
          thumbColor={preferences.has_disability_needs ? '#3498db' : '#f4f3f4'}
        />
      </View>
      
      <View style={styles.preference}>
        <View style={styles.preferenceIconContainer}>
          <Ionicons name="medkit" size={24} color="#3498db" />
        </View>
        <Text style={styles.preferenceText}>Esigenze sanitarie</Text>
        <Switch
          value={preferences.has_health_conditions}
          onValueChange={(value) => onPreferenceChange('has_health_conditions', value)}
          trackColor={{ false: '#d0d0d0', true: '#a0cfff' }}
          thumbColor={preferences.has_health_conditions ? '#3498db' : '#f4f3f4'}
        />
      </View>

      <View style={styles.divider} />
      
      <TouchableOpacity 
        style={styles.categoryButton}
        onPress={onManageCategories}
      >
        <View style={styles.categoryButtonContent}>
          <Ionicons name="list" size={22} color="#3498db" />
          <Text style={styles.categoryButtonText}>Gestisci categorie preferite</Text>
        </View>
        <Text style={styles.categoryCount}>
          {preferences.preferred_categories.length} selezionate
        </Text>
        <Ionicons name="chevron-forward" size={20} color="#999" />
      </TouchableOpacity>

      <TouchableOpacity 
        style={styles.categoryButton}
        onPress={onManageActivities}
      >
        <View style={styles.categoryButtonContent}>
          <Ionicons name="star" size={22} color="#3498db" />
          <Text style={styles.categoryButtonText}>Gestisci attività preferite</Text>
        </View>
        <Text style={styles.categoryCount}>
          {preferences.preferred_activities.length} selezionate
        </Text>
        <Ionicons name="chevron-forward" size={20} color="#999" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  preference: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  preferenceIconContainer: {
    marginRight: 12,
  },
  preferenceText: {
    flex: 1,
    fontSize: 16,
    color: '#444',
  },
  divider: {
    height: 1,
    backgroundColor: '#e0e0e0',
    marginVertical: 15,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  categoryButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryButtonText: {
    fontSize: 16,
    color: '#444',
    marginLeft: 10,
  },
  categoryCount: {
    fontSize: 14,
    color: '#999',
    marginRight: 5,
  }
});

export default PreferencesSection; 