import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, Image, TouchableOpacity } from 'react-native';
import { supabase } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';
import { UserBadge } from '../types/badge';
import { Ionicons } from '@expo/vector-icons';

export default function BadgesScreen({ navigation }) {
  const { user } = useAuth();
  const [userBadges, setUserBadges] = useState<UserBadge[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      fetchUserBadges();
    }
  }, [user]);

  const fetchUserBadges = async () => {
    try {
      const { data, error } = await supabase
        .from('user_badges')
        .select(`
          *,
          badge:badges (*)
        `)
        .eq('user_id', user?.id)
        .order('earned_at', { ascending: false });

      if (error) throw error;
      
      if (data) {
        setUserBadges(data);
      }
    } catch (error) {
      console.error('Error fetching user badges:', error);
    } finally {
      setLoading(false);
    }
  };

  const setFeaturedBadge = async (badgeId: string) => {
    try {
      await supabase
        .from('user_profiles')
        .update({ featured_badge_id: badgeId })
        .eq('id', user?.id);
      
      // Return to profile screen
      navigation.goBack();
    } catch (error) {
      console.error('Error setting featured badge:', error);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>I tuoi Badge</Text>
      
      {userBadges.length > 0 ? (
        <FlatList
          data={userBadges}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <TouchableOpacity 
              style={styles.badgeItem}
              onPress={() => setFeaturedBadge(item.id)}
            >
              <Image
                source={{ uri: item.badge?.icon_url }}
                style={styles.badgeImage}
              />
              <View style={styles.badgeInfo}>
                <Text style={styles.badgeName}>{item.badge?.name}</Text>
                <Text style={styles.badgeDescription}>{item.badge?.description}</Text>
                <Text style={styles.earnedDate}>
                  Ottenuto il: {new Date(item.earned_at).toLocaleDateString()}
                </Text>
              </View>
            </TouchableOpacity>
          )}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Ionicons name="trophy-outline" size={60} color="#999" />
          <Text style={styles.emptyText}>
            Non hai ancora sbloccato badge. Continua a esplorare!
          </Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  badgeItem: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    alignItems: 'center',
  },
  badgeImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 16,
  },
  badgeInfo: {
    flex: 1,
  },
  badgeName: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  badgeDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  earnedDate: {
    fontSize: 12,
    color: '#999',
    marginTop: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 16,
  },
});