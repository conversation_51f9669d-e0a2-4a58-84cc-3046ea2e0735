export type InteractionType = 
  | 'visit'           // Visita a un POI
  | 'check_in'        // Check-in in un luogo
  | 'achievement'     // Completamento di un achievement
  | 'weather_check'   // Controllo meteo
  | 'calendar_event'  // Interazione con evento in calendario
  | 'suggestion';     // Interazione con un suggerimento

export interface UserInteraction {
  id: string;
  user_id: string;
  type: InteractionType;
  poi_id?: string;
  achievement_id?: string;
  event_id?: string;
  timestamp: string;
  details?: Record<string, any>;
  location?: {
    latitude: number;
    longitude: number;
  };
}

export interface WeatherData {
  temperature: number;
  condition: string;
  wind_speed: number;
  wind_direction: string;
  humidity: number;
  precipitation: number;
  forecast: WeatherForecast[];
  timestamp: string;
  location: {
    latitude: number;
    longitude: number;
    name: string;
  };
}

export interface WeatherForecast {
  date: string;
  temperature_high: number;
  temperature_low: number;
  condition: string;
  precipitation_chance: number;
}

export interface CalendarEvent {
  id: string;
  title: string;
  description?: string;
  start_time: string;
  end_time?: string;
  location?: {
    latitude: number;
    longitude: number;
    name: string;
  };
  poi_id?: string;
  is_all_day: boolean;
  category: string;
  reminder_minutes?: number;
}

export interface Notification {
  id: string;
  user_id: string;
  title: string;
  message: string;
  type: 'general' | 'weather' | 'achievement' | 'event' | 'suggestion' | 'personal';
  timestamp: string;
  is_read: boolean;
  action_url?: string;
  poi_id?: string;
  event_id?: string;
  badge_id?: string;
} 