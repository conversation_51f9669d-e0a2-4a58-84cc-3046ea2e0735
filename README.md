# Il Giullare - App Guida Turistica

Applicazione mobile per la scoperta e l'esplorazione dell'isola, con funzionalità avanzate di profilo utente e gamification.

## Funzionalità Principali

### Sistema Location-Aware
- **Geofencing Intelligente**: Rilevamento automatico quando gli utenti si trovano fuori dall'isola di Tavolara
- **Adattamento Contestuale**: L'app adatta automaticamente contenuti e interfaccia in base alla posizione geografica dell'utente
- **Vista Mappa Estesa**: Copertura della mappa estesa dalla costa di Olbia a San Teodoro
- **Toggle Vista**: Pulsante per passare manualmente tra "Vista Isola" e "Vista Costa"
- **Transizioni Fluide**: Animazioni fluide quando gli utenti arrivano fisicamente sull'isola
- **Notifica di Arrivo**: Notifica di benvenuto quando l'utente raggiunge l'isola

### Categorie POI Context-Aware
**Per utenti sull'isola:**
- 🏖️ Spiagge
- ⛰️ Montagne
- 🍽️ Ristoranti
- 🚢 Porti
- 👑 Punti di interesse

**Per utenti fuori dall'isola:**
- 🚢 Punti di imbarco
- 🅿️ Parcheggi
- 🛥️ Noleggio barche/gommoni
- 🥾 Escursioni organizzate/trekking
- 🤿 Servizi diving/snorkeling
- 🎫 Biglietterie/punti informazione

### Sistema Badge Location-Based
- **Badge "Island Access"**: Assegnato automaticamente quando l'utente raggiunge fisicamente l'isola di Tavolara
- **Badge "Island Fan"**: Badge aggiuntivo assegnato quando l'utente visita l'isola due volte nella stessa settimana
- **Integrazione Profilo**: Sezione "Badge Ricevuti" nel profilo utente
- **Notifiche Achievement**: Sistema di notifiche per i nuovi badge ottenuti

### Profilo Utente Avanzato
- **Foto Profilo**: Carica e personalizza la tua immagine profilo
- **Badge**: Visualizza e metti in evidenza i badge ottenuti attraverso le tue esplorazioni
- **Preferenze di Viaggio**: Personalizza la tua esperienza con informazioni su:
  - Viaggi con animali domestici
  - Viaggi in famiglia
  - Esigenze di accessibilità
  - Condizioni di salute
  - Categorie preferite
  - Attività preferite

### Sistema di Achievement
- **Scopri il Regno**: Visita tutte le principali aree dell'isola
- **Tutte le Spiagge**: Scopri tutte le spiagge dell'isola
- **Fino in Vetta**: Raggiungi la cima della montagna
- **Tramonto al Belvedere**: Visita il belvedere al tramonto
- **Una Notte sull'Isola**: Soggiorna per almeno una notte sull'isola
- **Viste Subacquee**: Partecipa a un'attività subacquea

### Notifiche e Interazioni
- **Personalizzazione Notifiche**: Gestisci quali notifiche desideri ricevere
- **Categorie**: Eventi, meteo, achievement, suggerimenti, notifiche generali e personali
- **Integrazione con POI**: Interazioni con i punti di interesse dell'isola
- **Calendario**: Eventi e attività sincronizzati con il calendario
- **Meteo**: Informazioni e avvisi meteo in tempo reale

## Specifiche Tecniche

### Stack Tecnologico
- **Frontend**: React Native con Expo
- **Backend**: Supabase (PostgreSQL, funzioni, autenticazione)
- **Archiviazione**: Supabase Storage per immagini e file
- **Database**: PostgreSQL con trigger e funzioni per la gestione degli achievement

### Struttura del Database
- Profili utente
- Badge e achievement (inclusi badge location-based)
- POI (Points of Interest) con categorie context-aware
- Interazioni utente (incluse interazioni di geofencing)
- Eventi calendario
- Dati meteo
- Sistema di notifiche
- Geofencing e tracking posizione

## Istruzioni per lo Sviluppo

### Prerequisiti
- Node.js v14+
- Expo CLI
- Account Supabase

### Setup
1. Clona il repository
2. Esegui `npm install` per installare le dipendenze
3. Configura le variabili d'ambiente nel file `.env`
4. Esegui `npm start` per avviare l'app in modalità sviluppo

### Database Setup
1. Crea un nuovo progetto Supabase
2. Esegui lo script di setup dal file `database/schema.sql`
3. Configura le policy di sicurezza per le tabelle

## Prossimi Sviluppi
- Implementazione schermate dettaglio badge
- Sistema di amici e condivisione achievement
- Integrazione con social media
- Sistema di recensioni POI
- Statistiche visita avanzate
