import * as Location from 'expo-location';

// Tavolara Island boundaries (approximate polygon)
export const TAVOLARA_BOUNDARIES = [
  { latitude: 40.8950, longitude: 9.7150 }, // Southwest point
  { latitude: 40.8950, longitude: 9.7300 }, // Southeast point  
  { latitude: 40.9020, longitude: 9.7300 }, // Northeast point
  { latitude: 40.9020, longitude: 9.7150 }, // Northwest point
];

// Extended coast area (Olbia to San Teodoro)
export const COAST_REGION = {
  latitude: 40.8500,
  longitude: 9.6000,
  latitudeDelta: 0.3,
  longitudeDelta: 0.4,
};

// Island region (focused view)
export const ISLAND_REGION = {
  latitude: 40.8985,
  longitude: 9.7225,
  latitudeDelta: 0.015,
  longitudeDelta: 0.015,
};

export type LocationContext = 'on_island' | 'coast_area' | 'outside_area';

export interface GeofenceEvent {
  type: 'enter' | 'exit';
  context: LocationContext;
  timestamp: Date;
  location: Location.LocationObjectCoords;
}

export class GeofenceService {
  private listeners: ((event: GeofenceEvent) => void)[] = [];
  private currentContext: LocationContext = 'outside_area';
  private lastKnownLocation: Location.LocationObjectCoords | null = null;

  /**
   * Check if a point is inside Tavolara Island boundaries
   */
  isPointInTavolara(latitude: number, longitude: number): boolean {
    return this.isPointInPolygon(latitude, longitude, TAVOLARA_BOUNDARIES);
  }

  /**
   * Check if a point is in the extended coast area
   */
  isPointInCoastArea(latitude: number, longitude: number): boolean {
    const { latitude: centerLat, longitude: centerLng, latitudeDelta, longitudeDelta } = COAST_REGION;
    
    return (
      latitude >= centerLat - latitudeDelta / 2 &&
      latitude <= centerLat + latitudeDelta / 2 &&
      longitude >= centerLng - longitudeDelta / 2 &&
      longitude <= centerLng + longitudeDelta / 2
    );
  }

  /**
   * Determine location context based on coordinates
   */
  getLocationContext(latitude: number, longitude: number): LocationContext {
    if (this.isPointInTavolara(latitude, longitude)) {
      return 'on_island';
    } else if (this.isPointInCoastArea(latitude, longitude)) {
      return 'coast_area';
    } else {
      return 'outside_area';
    }
  }

  /**
   * Process location update and trigger geofence events
   */
  processLocationUpdate(location: Location.LocationObjectCoords): void {
    const newContext = this.getLocationContext(location.latitude, location.longitude);
    
    if (newContext !== this.currentContext) {
      // Trigger exit event for previous context
      if (this.lastKnownLocation) {
        this.triggerEvent({
          type: 'exit',
          context: this.currentContext,
          timestamp: new Date(),
          location: this.lastKnownLocation,
        });
      }

      // Trigger enter event for new context
      this.triggerEvent({
        type: 'enter',
        context: newContext,
        timestamp: new Date(),
        location,
      });

      this.currentContext = newContext;
    }

    this.lastKnownLocation = location;
  }

  /**
   * Add event listener
   */
  addListener(callback: (event: GeofenceEvent) => void): void {
    this.listeners.push(callback);
  }

  /**
   * Remove event listener
   */
  removeListener(callback: (event: GeofenceEvent) => void): void {
    const index = this.listeners.indexOf(callback);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * Get current location context
   */
  getCurrentContext(): LocationContext {
    return this.currentContext;
  }

  /**
   * Get appropriate map region based on context
   */
  getMapRegionForContext(context: LocationContext = this.currentContext) {
    switch (context) {
      case 'on_island':
        return ISLAND_REGION;
      case 'coast_area':
      case 'outside_area':
        return COAST_REGION;
      default:
        return ISLAND_REGION;
    }
  }

  /**
   * Check if user has been on island in the last week (for Island Fan badge)
   */
  async checkRecentIslandVisits(): Promise<boolean> {
    // This would typically check against stored visit history
    // For now, we'll implement a simple check
    return false; // Placeholder - implement with actual storage
  }

  private triggerEvent(event: GeofenceEvent): void {
    this.listeners.forEach(listener => listener(event));
  }

  private isPointInPolygon(latitude: number, longitude: number, polygon: Array<{latitude: number, longitude: number}>): boolean {
    let inside = false;
    const x = longitude;
    const y = latitude;

    for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
      const xi = polygon[i].longitude;
      const yi = polygon[i].latitude;
      const xj = polygon[j].longitude;
      const yj = polygon[j].latitude;

      if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
        inside = !inside;
      }
    }

    return inside;
  }
}

// Singleton instance
export const geofenceService = new GeofenceService();
