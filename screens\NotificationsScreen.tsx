import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { supabase } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';
import { Notification } from '../types/interaction';
import { Ionicons } from '@expo/vector-icons';

export default function NotificationsScreen({ navigation }) {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      fetchNotifications();
    }
  }, [user]);

  const fetchNotifications = async () => {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', user?.id)
        .order('timestamp', { ascending: false });

      if (error) throw error;
      
      if (data) {
        setNotifications(data);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleNotificationPress = async (notification: Notification) => {
    // Segna la notifica come letta
    try {
      await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notification.id);

      // Se la notifica ha un'azione (ad es. navigare a un POI)
      if (notification.poi_id) {
        navigation.navigate('Main', { 
          screen: 'Map',
          params: { poiId: notification.poi_id }
        });
      } else if (notification.event_id) {
        navigation.navigate('Main', {
          screen: 'Explore',
          params: { eventId: notification.event_id }
        });
      } else if (notification.badge_id) {
        navigation.navigate('BadgesScreen');
      }

      // Ricarica le notifiche
      fetchNotifications();
    } catch (error) {
      console.error('Error handling notification:', error);
    }
  };

  // Icona in base al tipo di notifica
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'general':
        return <Ionicons name="notifications" size={24} color="#3498db" />;
      case 'weather':
        return <Ionicons name="rainy" size={24} color="#f39c12" />;
      case 'achievement':
        return <Ionicons name="trophy" size={24} color="#2ecc71" />;
      case 'event':
        return <Ionicons name="calendar" size={24} color="#9b59b6" />;
      case 'suggestion':
        return <Ionicons name="bulb" size={24} color="#e74c3c" />;
      case 'personal':
        return <Ionicons name="person" size={24} color="#1abc9c" />;
      default:
        return <Ionicons name="notifications" size={24} color="#3498db" />;
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Le tue Notifiche</Text>
      
      {notifications.length > 0 ? (
        <FlatList
          data={notifications}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <TouchableOpacity 
              style={[
                styles.notificationItem,
                !item.is_read && styles.unreadNotification
              ]}
              onPress={() => handleNotificationPress(item)}
            >
              <View style={styles.iconContainer}>
                {getNotificationIcon(item.type)}
              </View>
              <View style={styles.notificationContent}>
                <Text style={styles.notificationTitle}>{item.title}</Text>
                <Text style={styles.notificationMessage}>{item.message}</Text>
                <Text style={styles.notificationTime}>
                  {new Date(item.timestamp).toLocaleString()}
                </Text>
              </View>
            </TouchableOpacity>
          )}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Ionicons name="notifications-off-outline" size={60} color="#999" />
          <Text style={styles.emptyText}>
            Non hai notifiche al momento
          </Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  notificationItem: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  unreadNotification: {
    backgroundColor: '#f0f8ff',
  },
  iconContainer: {
    marginRight: 16,
    justifyContent: 'center',
  },
  notificationContent: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  notificationMessage: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  notificationTime: {
    fontSize: 12,
    color: '#999',
    marginTop: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 16,
  },
});