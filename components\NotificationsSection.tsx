import React from 'react';
import { View, Text, StyleSheet, Switch, TouchableOpacity } from 'react-native';
import { NotificationSettings } from '../types/auth';
import { Notification } from '../types/interaction';
import { Ionicons } from '@expo/vector-icons';

interface NotificationsSectionProps {
  settings: NotificationSettings;
  recentNotifications: Notification[];
  onSettingChange: (key: keyof NotificationSettings, value: boolean) => void;
  onNotificationPress: (notification: Notification) => void;
  onViewAllPress: () => void;
}

const NotificationsSection: React.FC<NotificationsSectionProps> = ({
  settings,
  recentNotifications,
  onSettingChange,
  onNotificationPress,
  onViewAllPress,
}) => {
  // Icona in base al tipo di notifica
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'general':
        return <Ionicons name="notifications" size={20} color="#3498db" />;
      case 'weather':
        return <Ionicons name="rainy" size={20} color="#f39c12" />;
      case 'achievement':
        return <Ionicons name="trophy" size={20} color="#2ecc71" />;
      case 'event':
        return <Ionicons name="calendar" size={20} color="#9b59b6" />;
      case 'suggestion':
        return <Ionicons name="bulb" size={20} color="#e74c3c" />;
      case 'personal':
        return <Ionicons name="person" size={20} color="#1abc9c" />;
      default:
        return <Ionicons name="alert-circle" size={20} color="#95a5a6" />;
    }
  };

  // Formatta la data della notifica in un formato leggibile
  const formatNotificationTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.round(diffMs / 60000);
    const diffHours = Math.round(diffMs / 3600000);
    const diffDays = Math.round(diffMs / 86400000);

    if (diffMins < 60) {
      return `${diffMins} min fa`;
    } else if (diffHours < 24) {
      return `${diffHours} ore fa`;
    } else if (diffDays < 7) {
      return `${diffDays} giorni fa`;
    } else {
      return `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`;
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.headerRow}>
        <Text style={styles.sectionTitle}>Notifiche</Text>
        <TouchableOpacity onPress={onViewAllPress}>
          <Text style={styles.viewAllText}>Vedi tutte</Text>
        </TouchableOpacity>
      </View>

      {/* Impostazioni notifiche */}
      <Text style={styles.settingsTitle}>Impostazioni</Text>
      <View style={styles.settingsContainer}>
        <View style={styles.settingItem}>
          <Text style={styles.settingText}>Notifiche generali</Text>
          <Switch
            value={settings.general_notifications}
            onValueChange={(value) => onSettingChange('general_notifications', value)}
            trackColor={{ false: '#d0d0d0', true: '#a0cfff' }}
            thumbColor={settings.general_notifications ? '#3498db' : '#f4f3f4'}
          />
        </View>
        
        <View style={styles.settingItem}>
          <Text style={styles.settingText}>Eventi</Text>
          <Switch
            value={settings.event_notifications}
            onValueChange={(value) => onSettingChange('event_notifications', value)}
            trackColor={{ false: '#d0d0d0', true: '#a0cfff' }}
            thumbColor={settings.event_notifications ? '#3498db' : '#f4f3f4'}
          />
        </View>
        
        <View style={styles.settingItem}>
          <Text style={styles.settingText}>Allerte meteo</Text>
          <Switch
            value={settings.weather_alerts}
            onValueChange={(value) => onSettingChange('weather_alerts', value)}
            trackColor={{ false: '#d0d0d0', true: '#a0cfff' }}
            thumbColor={settings.weather_alerts ? '#3498db' : '#f4f3f4'}
          />
        </View>
        
        <View style={styles.settingItem}>
          <Text style={styles.settingText}>Suggerimenti POI</Text>
          <Switch
            value={settings.poi_suggestions}
            onValueChange={(value) => onSettingChange('poi_suggestions', value)}
            trackColor={{ false: '#d0d0d0', true: '#a0cfff' }}
            thumbColor={settings.poi_suggestions ? '#3498db' : '#f4f3f4'}
          />
        </View>
        
        <View style={styles.settingItem}>
          <Text style={styles.settingText}>Achievement sbloccati</Text>
          <Switch
            value={settings.achievement_notifications}
            onValueChange={(value) => onSettingChange('achievement_notifications', value)}
            trackColor={{ false: '#d0d0d0', true: '#a0cfff' }}
            thumbColor={settings.achievement_notifications ? '#3498db' : '#f4f3f4'}
          />
        </View>
        
        <View style={styles.settingItem}>
          <Text style={styles.settingText}>Notifiche personali</Text>
          <Switch
            value={settings.personal_notifications}
            onValueChange={(value) => onSettingChange('personal_notifications', value)}
            trackColor={{ false: '#d0d0d0', true: '#a0cfff' }}
            thumbColor={settings.personal_notifications ? '#3498db' : '#f4f3f4'}
          />
        </View>
      </View>

      {/* Notifiche recenti */}
      <Text style={styles.recentTitle}>Notifiche recenti</Text>
      
      {recentNotifications.length > 0 ? (
        <View style={styles.notificationsContainer}>
          {recentNotifications.map((notification) => (
            <TouchableOpacity 
              key={notification.id} 
              style={[
                styles.notificationItem,
                !notification.is_read && styles.unreadNotification
              ]}
              onPress={() => onNotificationPress(notification)}
            >
              <View style={styles.notificationIcon}>
                {getNotificationIcon(notification.type)}
              </View>
              <View style={styles.notificationContent}>
                <Text style={styles.notificationTitle}>{notification.title}</Text>
                <Text style={styles.notificationMessage} numberOfLines={2}>
                  {notification.message}
                </Text>
                <Text style={styles.notificationTime}>
                  {formatNotificationTime(notification.timestamp)}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      ) : (
        <View style={styles.emptyNotificationsContainer}>
          <Ionicons name="notifications-off-outline" size={40} color="#999" />
          <Text style={styles.emptyNotificationsText}>
            Non hai notifiche recenti
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  viewAllText: {
    color: '#3498db',
    fontWeight: '500',
  },
  settingsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
    color: '#333',
  },
  settingsContainer: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    marginBottom: 20,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#ebebeb',
  },
  settingText: {
    fontSize: 15,
    color: '#555',
  },
  recentTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
    color: '#333',
  },
  notificationsContainer: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
  },
  notificationItem: {
    flexDirection: 'row',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#ebebeb',
  },
  unreadNotification: {
    backgroundColor: '#f0f7ff',
  },
  notificationIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#eff4f9',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  notificationContent: {
    flex: 1,
  },
  notificationTitle: {
    fontWeight: '600',
    fontSize: 15,
    marginBottom: 4,
    color: '#333',
  },
  notificationMessage: {
    fontSize: 14,
    color: '#666',
    marginBottom: 6,
  },
  notificationTime: {
    fontSize: 12,
    color: '#999',
  },
  emptyNotificationsContainer: {
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
  },
  emptyNotificationsText: {
    marginTop: 10,
    color: '#999',
    textAlign: 'center',
  },
});

export default NotificationsSection; 