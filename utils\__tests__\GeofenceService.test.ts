import { GeofenceService, TAVOLARA_BOUNDARIES, COAST_REGION } from '../GeofenceService';

describe('GeofenceService', () => {
  let geofenceService: GeofenceService;

  beforeEach(() => {
    geofenceService = new GeofenceService();
  });

  describe('isPointInTavolara', () => {
    it('should return true for points inside Tavolara boundaries', () => {
      // Point inside the island
      const result = geofenceService.isPointInTavolara(40.8985, 9.7225);
      expect(result).toBe(true);
    });

    it('should return false for points outside Tavolara boundaries', () => {
      // Point in Olbia (outside island)
      const result = geofenceService.isPointInTavolara(40.9237, 9.5026);
      expect(result).toBe(false);
    });
  });

  describe('isPointInCoastArea', () => {
    it('should return true for points in the coast area', () => {
      // Point in Olbia (coast area)
      const result = geofenceService.isPointInCoastArea(40.9237, 9.5026);
      expect(result).toBe(true);
    });

    it('should return false for points outside coast area', () => {
      // Point far from coast
      const result = geofenceService.isPointInCoastArea(41.5, 10.0);
      expect(result).toBe(false);
    });
  });

  describe('getLocationContext', () => {
    it('should return "on_island" for Tavolara coordinates', () => {
      const context = geofenceService.getLocationContext(40.8985, 9.7225);
      expect(context).toBe('on_island');
    });

    it('should return "coast_area" for coast coordinates', () => {
      const context = geofenceService.getLocationContext(40.9237, 9.5026);
      expect(context).toBe('coast_area');
    });

    it('should return "outside_area" for distant coordinates', () => {
      const context = geofenceService.getLocationContext(41.5, 10.0);
      expect(context).toBe('outside_area');
    });
  });

  describe('processLocationUpdate', () => {
    it('should trigger enter event when moving to island', () => {
      const mockListener = jest.fn();
      geofenceService.addListener(mockListener);

      // Simulate moving to the island
      geofenceService.processLocationUpdate({
        latitude: 40.8985,
        longitude: 9.7225,
        accuracy: 10,
        altitude: 0,
        altitudeAccuracy: null,
        heading: null,
        speed: null,
      });

      expect(mockListener).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'enter',
          context: 'on_island',
        })
      );
    });

    it('should trigger exit event when leaving island', () => {
      const mockListener = jest.fn();
      geofenceService.addListener(mockListener);

      // First, move to island
      geofenceService.processLocationUpdate({
        latitude: 40.8985,
        longitude: 9.7225,
        accuracy: 10,
        altitude: 0,
        altitudeAccuracy: null,
        heading: null,
        speed: null,
      });

      // Clear previous calls
      mockListener.mockClear();

      // Then move to coast
      geofenceService.processLocationUpdate({
        latitude: 40.9237,
        longitude: 9.5026,
        accuracy: 10,
        altitude: 0,
        altitudeAccuracy: null,
        heading: null,
        speed: null,
      });

      expect(mockListener).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'exit',
          context: 'on_island',
        })
      );

      expect(mockListener).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'enter',
          context: 'coast_area',
        })
      );
    });
  });

  describe('getMapRegionForContext', () => {
    it('should return island region for on_island context', () => {
      const region = geofenceService.getMapRegionForContext('on_island');
      expect(region.latitudeDelta).toBe(0.015);
      expect(region.longitudeDelta).toBe(0.015);
    });

    it('should return coast region for coast_area context', () => {
      const region = geofenceService.getMapRegionForContext('coast_area');
      expect(region.latitudeDelta).toBe(0.3);
      expect(region.longitudeDelta).toBe(0.4);
    });
  });
});
