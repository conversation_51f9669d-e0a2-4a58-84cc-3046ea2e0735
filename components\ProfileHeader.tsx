import React from 'react';
import { View, Text, Image, StyleSheet, TouchableOpacity } from 'react-native';
import { UserProfile } from '../types/auth';
import { UserBadge } from '../types/badge';
import { Ionicons } from '@expo/vector-icons';

interface ProfileHeaderProps {
  userProfile: UserProfile;
  featuredBadge: UserBadge | null;
  onAvatarPress: () => void;
  onBadgePress: () => void;
}

const ProfileHeader: React.FC<ProfileHeaderProps> = ({
  userProfile,
  featuredBadge,
  onAvatarPress,
  onBadgePress,
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.headerContent}>
        <TouchableOpacity onPress={onAvatarPress} style={styles.avatarContainer}>
          {userProfile.avatar_url ? (
            <Image 
              source={{ uri: userProfile.avatar_url }} 
              style={styles.avatar} 
            />
          ) : (
            <View style={styles.avatarPlaceholder}>
              <Text style={styles.avatarInitial}>
                {userProfile.name.charAt(0).toUpperCase()}
              </Text>
            </View>
          )}
          <View style={styles.editIconContainer}>
            <Ionicons name="camera" size={16} color="#fff" />
          </View>
        </TouchableOpacity>

        <View style={styles.userInfo}>
          <Text style={styles.userName}>{userProfile.name}</Text>
          <Text style={styles.userEmail}>{userProfile.email}</Text>
        </View>
      </View>

      {featuredBadge ? (
        <TouchableOpacity 
          style={styles.badgeContainer}
          onPress={onBadgePress}
        >
          <Image 
            source={{ uri: featuredBadge.badge?.icon_url }} 
            style={styles.badgeIcon} 
          />
          <View style={styles.badgeInfo}>
            <Text style={styles.badgeName}>{featuredBadge.badge?.name}</Text>
            <Text style={styles.badgeDescription}>
              Badge in evidenza
            </Text>
          </View>
        </TouchableOpacity>
      ) : (
        <TouchableOpacity 
          style={styles.noBadgeContainer}
          onPress={onBadgePress}
        >
          <Ionicons name="trophy-outline" size={30} color="#999" />
          <Text style={styles.noBadgeText}>
            Sblocca badge completando achievement!
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 15,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 2,
    borderColor: '#3498db',
  },
  avatarPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#e0e0e0',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#3498db',
  },
  avatarInitial: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#555',
  },
  editIconContainer: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#3498db',
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    color: '#666',
  },
  badgeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9f9f9',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#eaeaea',
  },
  badgeIcon: {
    width: 50,
    height: 50,
    marginRight: 15,
  },
  badgeInfo: {
    flex: 1,
  },
  badgeName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  badgeDescription: {
    fontSize: 12,
    color: '#666',
  },
  noBadgeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9f9f9',
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#eaeaea',
    justifyContent: 'center',
  },
  noBadgeText: {
    marginLeft: 10,
    fontSize: 14,
    color: '#999',
  },
});

export default ProfileHeader; 