export interface UserProfile {
  id: string;
  email: string;
  name: string;
  avatar_url?: string;
  featured_badge_id?: string;
  preferences: UserPreferences;
  achievement_progress?: Record<string, boolean>;
  notification_settings?: NotificationSettings;
  created_at?: string;
  last_login?: string;
}

export interface UserPreferences {
  travels_with_pets: boolean;
  travels_with_family: boolean;
  has_disability_needs: boolean;
  has_health_conditions: boolean;
  preferred_categories: string[];
  preferred_activities: string[];
  other_preferences?: Record<string, any>;
}

export interface NotificationSettings {
  general_notifications: boolean;
  event_notifications: boolean;
  weather_alerts: boolean;
  poi_suggestions: boolean;
  achievement_notifications: boolean;
  personal_notifications: boolean;
}

export interface AuthError {
  message: string;
}