import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { NavigationContainer, DefaultTheme } from '@react-navigation/native';
import MapScreen from '../screens/MapScreen';
import ExploreScreen from '../screens/ExploreScreen';
import ProfileScreen from '../screens/ProfileScreen';
import AuthScreen from '../screens/AuthScreen';
import BadgesScreen from '../screens/BadgesScreen';
import NotificationsScreen from '../screens/NotificationsScreen';
import { useAuth } from '../contexts/AuthContext';
import { ActivityIndicator, View } from 'react-native';

const BottomTab = createBottomTabNavigator();
const Stack = createStackNavigator();

// Create a custom theme that forces initial route
const MyTheme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    background: 'white',
  },
};

function MainNavigator() {
  const { user } = useAuth();
  
  return (
    <BottomTab.Navigator 
      initialRouteName="Map"
      screenOptions={{
        headerShown: true,
        tabBarActiveTintColor: '#0066cc',
      }}
    >
      <BottomTab.Screen 
        name="Map" 
        component={MapScreen}
        options={{
          tabBarLabel: 'Map',
        }}
      />
      <BottomTab.Screen 
        name="Explore" 
        component={ExploreScreen}
        options={{
          tabBarLabel: 'Explore',
        }}
      />
      <BottomTab.Screen 
        name="Profile" 
        component={user ? ProfileScreen : AuthScreen}
        listeners={{
          tabPress: e => {
            // Don't do anything special on tab press
          },
        }}
        options={{
          tabBarLabel: user ? 'Profile' : 'Sign In',
        }}
      />
    </BottomTab.Navigator>
  );
}

export default function Navigation() {
  const { loading } = useAuth();
  
  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#0066cc" />
      </View>
    );
  }

  return (
    <NavigationContainer theme={MyTheme}>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        <Stack.Screen name="Main" component={MainNavigator} />
        <Stack.Screen name="BadgesScreen" component={BadgesScreen} options={{ headerShown: true, title: 'I tuoi Badge' }} />
        <Stack.Screen name="NotificationsScreen" component={NotificationsScreen} options={{ headerShown: true, title: 'Notifiche' }} />
      </Stack.Navigator>
    </NavigationContainer>
  );
}
